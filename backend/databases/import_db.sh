#!/bin/bash

# Database Import Script for Kanban RPG
# Usage: ./import_db.sh <export_file> [--fresh] [--migrate]
# 
# Options:
#   --fresh   : Create a fresh database (removes existing data)
#   --migrate : Run migrations after import

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Change to project root
cd "$PROJECT_ROOT"

# Parse arguments
EXPORT_FILE=""
FRESH_DB=false
RUN_MIGRATIONS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --fresh)
            FRESH_DB=true
            shift
            ;;
        --migrate)
            RUN_MIGRATIONS=true
            shift
            ;;
        *)
            if [[ -z "$EXPORT_FILE" ]]; then
                EXPORT_FILE="$1"
            else
                echo "Error: Unknown option $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if export file is provided
if [[ -z "$EXPORT_FILE" ]]; then
    echo "Usage: $0 <export_file> [--fresh] [--migrate]"
    echo ""
    echo "Available export files:"
    ls -la backend/databases/*.{sql,dump} 2>/dev/null || echo "No export files found"
    exit 1
fi

# Check if export file exists
if [[ ! -f "$EXPORT_FILE" ]]; then
    echo "Error: Export file '$EXPORT_FILE' not found"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose is not installed or not in PATH"
    exit 1
fi

echo "🗄️  Database Import Script"
echo "📁 Import file: $EXPORT_FILE"
echo "🔄 Fresh database: $FRESH_DB"
echo "🚀 Run migrations: $RUN_MIGRATIONS"
echo ""

# Determine file type
if [[ "$EXPORT_FILE" == *.sql ]]; then
    FILE_TYPE="sql"
elif [[ "$EXPORT_FILE" == *.dump ]]; then
    FILE_TYPE="dump"
else
    echo "Error: Unsupported file type. Use .sql or .dump files"
    exit 1
fi

# Fresh database setup
if [[ "$FRESH_DB" == true ]]; then
    echo "🛑 Stopping containers..."
    docker-compose down
    
    echo "🗑️  Removing existing database volume..."
    docker volume rm kanban-rpg_postgres_data 2>/dev/null || true
    
    echo "🚀 Starting database..."
    docker-compose up -d db
    
    echo "⏳ Waiting for database to be ready..."
    sleep 10
    
    # Wait for database to be healthy
    while ! docker-compose exec db pg_isready -U kanban_user -d postgres &>/dev/null; do
        echo "   Still waiting for database..."
        sleep 5
    done
fi

# Ensure database is running
if ! docker-compose ps | grep -q "kanban-rpg_db_1.*Up"; then
    echo "🚀 Starting database..."
    docker-compose up -d db
    sleep 10
fi

# Copy file to container
CONTAINER_FILE="/tmp/import_file"
echo "📋 Copying export file to database container..."
docker cp "$EXPORT_FILE" kanban-rpg_db_1:"$CONTAINER_FILE"

# Import based on file type
echo "📥 Importing database..."
if [[ "$FILE_TYPE" == "sql" ]]; then
    if [[ "$FRESH_DB" == true ]]; then
        docker-compose exec -T db psql -U kanban_user -d postgres -f "$CONTAINER_FILE"
    else
        docker-compose exec -T db psql -U kanban_user -d kanban_rpg -f "$CONTAINER_FILE"
    fi
else
    if [[ "$FRESH_DB" == true ]]; then
        docker-compose exec -T db pg_restore -U kanban_user -d postgres --clean --create "$CONTAINER_FILE"
    else
        docker-compose exec -T db pg_restore -U kanban_user -d kanban_rpg --clean "$CONTAINER_FILE"
    fi
fi

# Clean up temporary file
docker-compose exec db rm "$CONTAINER_FILE"

# Run migrations if requested
if [[ "$RUN_MIGRATIONS" == true ]]; then
    echo "🔄 Starting backend for migrations..."
    docker-compose up -d backend
    
    echo "⏳ Waiting for backend to be ready..."
    sleep 15
    
    echo "📊 Checking current migration state..."
    docker-compose exec backend alembic current
    
    echo "🚀 Running migrations..."
    docker-compose exec backend alembic upgrade head
    
    echo "✅ Migration completed"
fi

echo ""
echo "✅ Database import completed successfully!"
echo "🔍 You can verify the import with:"
echo "   docker-compose exec db psql -U kanban_user -d kanban_rpg -c '\\dt'"
