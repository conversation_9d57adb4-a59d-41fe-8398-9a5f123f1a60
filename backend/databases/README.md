# Database Export and Import Guide

This directory contains database exports for the Kanban RPG application.

## Export Files

- `*.sql` - Plain text SQL dump files (human readable)
- `*.dump` - Custom format PostgreSQL dump files (binary, more efficient)

## Import Instructions

### Prerequisites

1. Ensure Docker and Docker Compose are installed
2. Make sure you're in the project root directory (`/home/<USER>/kanban-rpg`)
3. Have the `.env` file with correct database credentials

### Method 1: Import to Fresh Database (Recommended)

This method creates a completely new database from the export:

```bash
# 1. Stop the current containers
docker-compose down

# 2. Remove the existing database volume (WARNING: This deletes current data)
docker volume rm kanban-rpg_postgres_data

# 3. Start only the database service
docker-compose up -d db

# 4. Wait for database to be ready (check with)
docker-compose logs db

# 5. Import from SQL file
docker-compose exec db psql -U kanban_user -d postgres -f /tmp/export.sql

# OR import from custom dump file (more reliable)
docker-compose exec db pg_restore -U kanban_user -d postgres --clean --create /tmp/export.dump
```

### Method 2: Import to Existing Database

This method imports into an existing database (may cause conflicts):

```bash
# 1. Copy the export file into the database container
docker cp backend/databases/kanban_rpg_export_YYYYMMDD_HHMMSS.sql kanban-rpg_db_1:/tmp/export.sql

# 2. Import the SQL file
docker-compose exec db psql -U kanban_user -d kanban_rpg -f /tmp/export.sql

# OR for custom dump format
docker cp backend/databases/kanban_rpg_export_YYYYMMDD_HHMMSS.dump kanban-rpg_db_1:/tmp/export.dump
docker-compose exec db pg_restore -U kanban_user -d kanban_rpg --clean /tmp/export.dump
```

### Method 3: Import from Previous Migration

If you're importing a database from a previous migration state:

```bash
# 1. Follow Method 1 steps 1-4 to create fresh database

# 2. Import the old database
docker cp backend/databases/kanban_rpg_export_YYYYMMDD_HHMMSS.sql kanban-rpg_db_1:/tmp/export.sql
docker-compose exec db psql -U kanban_user -d postgres -f /tmp/export.sql

# 3. Start the backend service to run any pending migrations
docker-compose up -d backend

# 4. Check migration status
docker-compose exec backend alembic current

# 5. Run any pending migrations
docker-compose exec backend alembic upgrade head

# 6. Verify the database structure
docker-compose exec db psql -U kanban_user -d kanban_rpg -c "\dt"
```

## Troubleshooting

### Permission Errors
If you get permission errors, ensure the database user has proper privileges:

```bash
docker-compose exec db psql -U kanban_user -d postgres -c "ALTER USER kanban_user CREATEDB;"
```

### Migration Conflicts
If you encounter migration conflicts after import:

```bash
# Check current migration state
docker-compose exec backend alembic current

# If needed, mark migrations as applied without running them
docker-compose exec backend alembic stamp head

# Or downgrade and re-run migrations
docker-compose exec backend alembic downgrade base
docker-compose exec backend alembic upgrade head
```

### Database Connection Issues
If the backend can't connect to the database:

```bash
# Check if database is healthy
docker-compose ps

# Check database logs
docker-compose logs db

# Test connection manually
docker-compose exec db psql -U kanban_user -d kanban_rpg -c "SELECT 1;"
```

## Creating New Exports

To create a new export of the current database:

```bash
# SQL format (human readable)
docker-compose exec db pg_dump -U kanban_user -d kanban_rpg --clean --if-exists --create > backend/databases/kanban_rpg_export_$(date +%Y%m%d_%H%M%S).sql

# Custom format (more efficient, recommended for large databases)
docker-compose exec db pg_dump -U kanban_user -d kanban_rpg --verbose --clean --if-exists --create --format=custom > backend/databases/kanban_rpg_export_$(date +%Y%m%d_%H%M%S).dump
```

## Notes

- Always backup your current database before importing
- The `--clean --if-exists` flags ensure a clean import by dropping existing objects
- Custom format dumps (`.dump`) are more efficient and reliable for large databases
- SQL format dumps (`.sql`) are human-readable and can be edited if needed
- When importing from previous migrations, always run `alembic upgrade head` afterward
