#!/bin/bash

# Database Export Script for Kanban RPG
# Usage: ./export_db.sh [format]
# format: sql (default) or dump

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Change to project root
cd "$PROJECT_ROOT"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose is not installed or not in PATH"
    exit 1
fi

# Check if database is running
if ! docker-compose ps | grep -q "kanban-rpg_db_1.*Up"; then
    echo "Error: Database container is not running. Please start it with 'docker-compose up -d db'"
    exit 1
fi

# Get format parameter (default to sql)
FORMAT=${1:-sql}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "Creating database export..."
echo "Format: $FORMAT"
echo "Timestamp: $TIMESTAMP"

case $FORMAT in
    "sql")
        FILENAME="kanban_rpg_export_${TIMESTAMP}.sql"
        echo "Exporting to: backend/databases/$FILENAME"
        docker-compose exec -T db pg_dump -U kanban_user -d kanban_rpg --clean --if-exists --create > "backend/databases/$FILENAME"
        ;;
    "dump")
        FILENAME="kanban_rpg_export_${TIMESTAMP}.dump"
        echo "Exporting to: backend/databases/$FILENAME"
        docker-compose exec -T db pg_dump -U kanban_user -d kanban_rpg --verbose --clean --if-exists --create --format=custom > "backend/databases/$FILENAME"
        ;;
    *)
        echo "Error: Invalid format '$FORMAT'. Use 'sql' or 'dump'"
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    echo "✅ Export completed successfully: backend/databases/$FILENAME"
    echo "📁 File size: $(du -h "backend/databases/$FILENAME" | cut -f1)"
else
    echo "❌ Export failed"
    exit 1
fi
