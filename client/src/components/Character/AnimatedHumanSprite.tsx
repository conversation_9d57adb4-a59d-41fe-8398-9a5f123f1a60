import React, { useEffect, useRef, useState } from 'react';
import { Application, Container, AnimatedSprite, Texture, Rectangle, Graphics } from 'pixi.js';
import colors from '@/utils/colors';

interface AnimatedHumanSpriteProps {
  width?: number | string;
  height?: number;
}



export const AnimatedHumanSprite: React.FC<AnimatedHumanSpriteProps> = ({
  width = 400,
  height = 288,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const appRef = useRef<Application | null>(null);
  const [renderingFailed, setRenderingFailed] = useState<boolean>(false);

  useEffect(() => {
    if (!canvasRef.current || appRef.current) return;

    const initializePixi = async () => {
      const canvas = canvasRef.current;
      if (!canvas || appRef.current) return;

      // Get the actual container dimensions
      const container = canvas.parentElement;
      const actualWidth = width === "100%" && container ? container.clientWidth : (typeof width === 'number' ? width : 400);
      const actualHeight = height;

      canvas.width = actualWidth;
      canvas.height = actualHeight;

      // Try WebGL first, fallback to canvas if shader issues occur
      let app;
      try {
        app = new Application({
          view: canvas,
          width: actualWidth,
          height: actualHeight,
          backgroundColor: colors.core.white,
          antialias: false,
          resolution: 1,
          autoDensity: false,
          powerPreference: 'default',
          resizeTo: undefined,
          sharedTicker: false,
        });
      } catch (webglError) {
        console.warn('WebGL failed, falling back to canvas renderer:', webglError);
        try {
          app = new Application({
            view: canvas,
            width: actualWidth,
            height: actualHeight,
            backgroundColor: 0x87CEEB,
            antialias: false,
            resolution: 1,
            autoDensity: false,
            powerPreference: 'default',
            resizeTo: undefined,
            forceCanvas: true,
            sharedTicker: false,
          });
        } catch (canvasError) {
          console.error('Both WebGL and Canvas rendering failed:', canvasError);
          setRenderingFailed(true);
          return;
        }
      }

      appRef.current = app;

      try {
        // Load all sprite textures in proper layering order with z-index
        const spriteConfig = [
          // Back layer (behind torso)
          { file: 'SkinArmRight.png', zIndex: 1 },
          { file: 'OutlineArmRight.png', zIndex: 2 },
          { file: 'SkinHandRight.png', zIndex: 3 },
          { file: 'OutlineHandRight.png', zIndex: 4 },

          // Middle layer (torso and legs)
          { file: 'SkinLegLeft.png', zIndex: 5 },
          { file: 'SkinLegRight.png', zIndex: 6 },
          { file: 'OutlineLegLeft.png', zIndex: 7 },
          { file: 'OutlineLegRight.png', zIndex: 8 },
          { file: 'SkinFeetLeft.png', zIndex: 9 },
          { file: 'SkinFeetRight.png', zIndex: 10 },
          { file: 'OutlineFeetLeft.png', zIndex: 11 },
          { file: 'OutlineFeetRight.png', zIndex: 12 },
          { file: 'SkinTorso.png', zIndex: 13 },
          { file: 'OutlineTorso.png', zIndex: 14 },

          // Front layer (in front of torso)
          { file: 'SkinArmLeft.png', zIndex: 15 },
          { file: 'OutlineArmLeft.png', zIndex: 16 },
          { file: 'SkinHandLeft.png', zIndex: 17 },
          { file: 'OutlineHandLeft.png', zIndex: 18 },

          // Top layer (head)
          { file: 'SkinHead.png', zIndex: 19 },
          { file: 'OutlineHead.png', zIndex: 20 }
        ];

        const basePath = '/sprites/baseHuman/standing1/';
        
        // Create animated sprites for each body part
        const animatedSprites: AnimatedSprite[] = [];
        const characterContainer = new Container();
        characterContainer.x = actualWidth / 2;
        characterContainer.y = actualHeight / 2;
        app.stage.addChild(characterContainer);

        console.log('Starting to load sprites...');
        console.log('Base path:', basePath);
        console.log('Sprite config:', spriteConfig);

        for (let index = 0; index < spriteConfig.length; index++) {
          const { file: spriteFile, zIndex } = spriteConfig[index];
          const fullPath = basePath + spriteFile;
          try {
            console.log(`Loading sprite: ${fullPath} (z-index: ${zIndex})`);

            // Load the texture directly
            const texture = await Texture.fromURL(fullPath);
            console.log(`Loaded texture for ${spriteFile}:`, texture.width, 'x', texture.height);

            // Create frames for animation (assuming 4 frames in a 512x128 spritesheet)
            const frames: Texture[] = [];
            const frameWidth = 128;
            const frameHeight = 128;
            const numFrames = Math.floor(texture.width / frameWidth); // Calculate frames based on actual texture width

            console.log(`Creating ${numFrames} frames for ${spriteFile}`);

            for (let i = 0; i < numFrames; i++) {
              const frameTexture = new Texture(
                texture.baseTexture,
                new Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
              );
              frames.push(frameTexture);
            }

            // Only create animated sprite if we have frames
            if (frames.length > 0) {
              try {
                const animatedSprite = new AnimatedSprite(frames);

                // Set a slower, more compatible animation speed
                animatedSprite.animationSpeed = 0.03; // Slower speed to avoid shader issues

                // Don't start animation yet - we'll sync them all later

                // Position sprite in the center
                animatedSprite.anchor.set(0.5);
                animatedSprite.x = 0; // Relative to container
                animatedSprite.y = 0; // Relative to container

                // Set z-index for proper layering
                animatedSprite.zIndex = zIndex;

                // Scale down if needed to fit in the container
                const scale = Math.min(actualWidth / frameWidth, actualHeight / frameHeight) * 0.8;
                animatedSprite.scale.set(scale);

                characterContainer.addChild(animatedSprite);
                animatedSprites.push(animatedSprite);
                console.log(`Successfully added animated sprite for ${spriteFile} with z-index ${zIndex}`);
              } catch (spriteError) {
                console.warn(`Failed to create animated sprite for ${spriteFile}:`, spriteError);
              }
            }

          } catch (error) {
            console.warn(`Failed to load sprite: ${spriteFile}`, error);
          }
        }

        // Enable z-index sorting for the character container
        characterContainer.sortableChildren = true;

        // Start all animations synchronously
        console.log(`Starting synchronized animation for ${animatedSprites.length} sprites`);
        animatedSprites.forEach((sprite, index) => {
          // Set all sprites to the same frame to ensure sync
          sprite.currentFrame = 0;
          sprite.play();
          console.log(`Started animation for sprite ${index}`);
        });

      } catch (error) {
        console.error('Failed to initialize sprites:', error);

        // Try to create a simple fallback if app exists
        if (app && app.stage) {
          try {
            const fallbackContainer = new Container();
            fallbackContainer.x = actualWidth / 2;
            fallbackContainer.y = actualHeight / 2;

            // Create a simple placeholder graphic
            const placeholder = new Graphics();
            placeholder.beginFill(0xFF0000);
            placeholder.drawCircle(0, 0, 50);
            placeholder.endFill();

            fallbackContainer.addChild(placeholder);
            app.stage.addChild(fallbackContainer);
          } catch (fallbackError) {
            console.error('Even fallback failed:', fallbackError);
          }
        }
      }
    };

    const timeoutId = setTimeout(initializePixi, 0);

    return () => {
      clearTimeout(timeoutId);
      if (appRef.current) {
        try {
          // Stop all animations before destroying
          appRef.current.stage.removeChildren();
          appRef.current.destroy(true, {
            children: true,
            texture: false,
            baseTexture: false
          });
          appRef.current = null;
        } catch (error) {
          console.warn('Error during PixiJS cleanup:', error);
        }
      }
    };
  }, [width, height]);

  // Show fallback message if rendering completely failed
  if (renderingFailed) {
    return (
      <div className="w-full flex justify-center items-center game-container">
        <p>WebGL is disabled</p>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center items-center game-container">
      <canvas
        ref={canvasRef}
        style={{
          width: width === "100%" ? '100%' : width,
          height: height,
          maxWidth: '100%'
        }}
      />
    </div>
  );
};
